@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-700;
  }

  body {
    @apply bg-cyber-dark text-white;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(0, 170, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(170, 0, 255, 0.05) 0%, transparent 50%);
    background-attachment: fixed;
  }
}

@layer components {
  .cyber-card {
    @apply bg-cyber-gray/50 backdrop-blur-cyber border border-cyber-green/20 rounded-lg p-6 shadow-cyber;
    background-image: linear-gradient(135deg, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
    transition: all 0.3s ease;
  }

  .cyber-card:hover {
    @apply border-cyber-green/40 shadow-neon;
    transform: translateY(-2px);
  }

  .cyber-button {
    @apply bg-cyber-green/20 hover:bg-cyber-green/30 border border-cyber-green/50 hover:border-cyber-green text-cyber-green font-semibold px-4 py-2 rounded-lg transition-all duration-300;
  }

  .cyber-button:hover {
    @apply shadow-neon;
  }

  .cyber-button-secondary {
    @apply bg-cyber-light/50 hover:bg-cyber-light/70 border border-cyber-green/30 hover:border-cyber-green/50 text-white font-semibold px-4 py-2 rounded-lg transition-all duration-300;
  }

  .cyber-input {
    @apply bg-cyber-gray/30 border border-cyber-green/30 focus:border-cyber-green focus:ring-1 focus:ring-cyber-green/50 rounded-lg px-3 py-2 text-white placeholder-gray-400 transition-all duration-300;
  }

  .cyber-nav-item {
    @apply flex items-center space-x-2 px-4 py-3 rounded-lg transition-all duration-300 text-gray-300 hover:text-cyber-green hover:bg-cyber-green/10;
  }

  .cyber-nav-item.active {
    @apply text-cyber-green bg-cyber-green/20 border-l-2 border-cyber-green shadow-cyber-inset;
  }

  .status-indicator {
    @apply w-3 h-3 rounded-full animate-pulse-neon;
  }

  .status-online {
    @apply bg-cyber-green shadow-neon;
  }

  .status-warning {
    @apply bg-cyber-yellow shadow-[0_0_20px_rgba(255,170,0,0.3)];
  }

  .status-error {
    @apply bg-cyber-red shadow-[0_0_20px_rgba(255,0,85,0.3)];
  }

  .cyber-grid {
    @apply grid gap-6 animate-slide-up;
  }

  .cyber-text-glow {
    @apply text-cyber-green animate-glow;
  }

  .cyber-border-glow {
    @apply border border-cyber-green/50 shadow-neon;
  }
}
